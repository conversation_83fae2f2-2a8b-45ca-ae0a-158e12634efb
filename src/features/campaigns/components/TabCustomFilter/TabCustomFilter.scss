.main-grid {
	display: grid;
	grid-template-columns: 20% 80%;
	grid-template-rows: 30% 70%;
	height: 700px; /* Altura fixa para visualização */
	gap: 0; /* Remove gap entre elementos */
	margin: -24px; /* Compensa o padding do container pai em todas as direções */
}

.campaigns-sidebar {
	grid-row: 1 / 3; /* <PERSON>cupa as duas linhas (100% vertical) */
	grid-column: 1;
	background-color: #E3F2FD; /* Azul claro */
	padding: 16px;
	border: 1px solid #BBDEFB;
	
	h3 {
		margin: 0;
		color: #1976D2;
		font-size: 16px;
		font-weight: bold;
	}
}

.filters-section {
	grid-row: 1;
	grid-column: 2;
	background-color: #E8F5E8; /* Verde claro */
	padding: 16px;
	border: 1px solid #C8E6C9;
	
	h3 {
		margin: 0;
		color: #388E3C;
		font-size: 16px;
		font-weight: bold;
	}
}

.chart-section {
	grid-row: 2;
	grid-column: 2;
	background-color: #FDE7F3; /* Rosa claro */
	padding: 16px;
	border: 1px solid #F8BBD9;
	
	h3 {
		margin: 0;
		color: #C2185B;
		font-size: 16px;
		font-weight: bold;
	}
}

.filter-title {
	color: var(--farm-primary-darken);
	font-weight: bold;
}

.campaign-card {
	background-color: #ECF6DC;
	border-radius: 5px;
	padding: 16px;
	margin-bottom: 8px;
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.filters-container {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	align-items: center;
}

.filter-group {
	flex: 1;
	min-width: 10px;
	
	.farm-label {
		display: block;
		margin-bottom: 8px;
	}
}

// Estilo específico para o select de Produto Comercial
.commercial-product-group {
	min-width: 250px; // Largura mínima para acomodar os nomes longos
	
	.commercial-product-select {
		min-width: 100%;
		width: 100%;
	}
}

.buttons-group {
	display: flex;
	
	flex-shrink: 0;
	
	.farm-btn {
		white-space: nowrap;
	}
}

// Responsividade para telas menores
@media (max-width: 768px) {
	.filters-container {
		flex-direction: column;
		align-items: stretch;
	}
	
	.filter-group {
		min-width: auto;
		width: 100%;
	}
	
	// Mantém largura mínima do produto comercial mesmo em mobile
	.commercial-product-group {
		min-width: 100%;
	}
	
	.buttons-group {
		width: 100%;
		justify-content: flex-start;
		margin-top: 8px;
	}
} 