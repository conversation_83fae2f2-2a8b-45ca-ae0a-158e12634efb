export const format = value => {
	if (typeof value !== 'string') {
		return value;
	}
	const removeMask = value.replace('R$', '');
	const removePont = removeMask.replace('.', '');
	const cleanedValue = removePont.replace(',', '.');
	return parseFloat(cleanedValue) || 0;
};

export function formatPlSum(formQuantity, formValue, formInput) {
	const quantity = format(formQuantity);
	const value = format(formValue);
	const valueInput = format(formInput);
	const result = (quantity + valueInput) * value;
	const formattedResult = result.toFixed(4).toString().replace('.', ',');
	return formattedResult;
}

export function formatPlSubtraction(formQuantity, formValue, formInput, valueCurrent) {
	const quantityCurrent = format(formQuantity);
	const valueInput = format(formValue);
	const quantityInput = format(formInput);
	const result = (quantityCurrent - quantityInput) * (parseFloat(valueCurrent) - valueInput);
	const formattedResult = result.toFixed(4).toString().replace('.', ',');
	return formattedResult;
}
