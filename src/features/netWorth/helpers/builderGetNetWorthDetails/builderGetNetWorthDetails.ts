
import { hasPropsObject } from '../hasPropsObject';

export const builderGetNetWorthDetails = (data, labels) => {

	const model = {
		quotas: [],
		totalNetWorth: 0,
		totalQuantity: 0
	};

	const modelRealNetWorth = {
		netWorth: 0,
		quantity: 0
	};
	const newData = labels.map((item) => {
		if(item === 'realNetWorth'){
			return {
				jr: hasPropsObject(data[item], 'jr') ? data[item]['jr'] : modelRealNetWorth,
				mz: hasPropsObject(data[item], 'mz') ? data[item]['mz'] : modelRealNetWorth,
				sr: hasPropsObject(data[item], 'sr') ? data[item]['sr'] : modelRealNetWorth,
				unique: hasPropsObject(data[item], 'unique') ? data[item]['unique'] : modelRealNetWorth,
			};
		}
		return {
			jr: hasPropsObject(data[item], 'jr') ? data[item]['jr'] : model,
			mz: hasPropsObject(data[item], 'mz') ? data[item]['mz'] : model,
			sr: hasPropsObject(data[item], 'sr') ? data[item]['sr'] : model,
			unique: hasPropsObject(data[item], 'unique') ? data[item]['unique'] : model,
		};
	});

	return {
		...data,
		currentNetWorth: {...newData[0]},
		initialNetWorth: {...newData[1]},
		realNetWorth: {...newData[2]}
	};
};
