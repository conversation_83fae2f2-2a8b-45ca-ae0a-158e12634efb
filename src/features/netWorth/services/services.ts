import registerV3Service from '@/configurations/services/registerV3';
import { RequestQueryString } from '@/types';
import { NetWorthQuotaPayload } from './../types';

const client = registerV3Service;

export const fetchNetWorthList = async (params?: RequestQueryString) => {
	return client.get('/api/v1/net-worth', {
		params,
	});
};

export const fetchNetWorthDetails = async (vehicleId: number) => {
	return client.get(`/api/v1/net-worth/financial-vehicle/${vehicleId}`);
};

export const postNetWorthIntegralization = async (
	vehicleId: number,
	payload: NetWorthQuotaPayload
) => {
	return client.post(`/api/v1/net-worth/financial-vehicle/${vehicleId}`, payload);
};

export const fetchNetWorthEditDetailsService = async (vehicleId: number) => {
	return client.get(`/api/v1/net-worth/financial-vehicle/${vehicleId}/details`);
};

export const patchNetWorthAmortization = async (vehicleId: number, payload: NetWorthQuotaPayload) => {
	return client.patch(`/api/v1/net-worth/financial-vehicle/${vehicleId}`, payload);
};

export const fetchNetWorthHistoryUsers = async (vehicleId: number) => {
	return client.get(`/api/v1/net-worth/history/${vehicleId}/users`);
};

export const fetchNetWorthHistory = async (vehicleId: number, params: RequestQueryString) => {
	return client.get(`/api/v1/net-worth/history/${vehicleId}`, {
		params,
	});
};
